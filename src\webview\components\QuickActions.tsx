import React from 'react';
import { Button } from './ui/button';
import { Card, CardContent } from './ui/card';

interface QuickActionsProps {
  onActionClick: (prompt: string) => void;
  disabled?: boolean;
}

const QuickActions: React.FC<QuickActionsProps> = ({ onActionClick, disabled }) => {
  const quickActions = [
    {
      icon: '🔍',
      title: '代码审查',
      prompt: '请帮我审查这段代码，指出可能的问题和改进建议'
    },
    {
      icon: '🚀',
      title: '性能优化',
      prompt: '请分析这段代码的性能，并提供优化建议'
    },
    {
      icon: '🐛',
      title: '调试帮助',
      prompt: '我的代码有bug，请帮我分析可能的原因'
    },
    {
      icon: '📚',
      title: '代码解释',
      prompt: '请详细解释这段代码的工作原理'
    },
    {
      icon: '✨',
      title: '重构建议',
      prompt: '请帮我重构这段代码，使其更加清晰和可维护'
    },
    {
      icon: '🧪',
      title: '编写测试',
      prompt: '请为这段代码编写单元测试'
    }
  ];

  return (
    <Card className="mb-4">
      <CardContent className="p-4">
        <h3 className="text-sm font-medium text-muted-foreground mb-3">快速操作</h3>
        <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
          {quickActions.map((action, index) => (
            <Button
              key={index}
              variant="outline"
              size="sm"
              className="h-auto p-3 flex flex-col items-center gap-1 text-xs"
              onClick={() => onActionClick(action.prompt)}
              disabled={disabled}
            >
              <span className="text-lg">{action.icon}</span>
              <span className="text-center leading-tight">{action.title}</span>
            </Button>
          ))}
        </div>
      </CardContent>
    </Card>
  );
};

export default QuickActions;
