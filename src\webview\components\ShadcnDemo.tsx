import React from 'react';
import { But<PERSON> } from './ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from './ui/card';

const ShadcnDemo: React.FC = () => {
  return (
    <div className="p-6 space-y-6">
      <p className="text-4xl font-bold text-gray-900">Tailwind CSS v4 + shadcn/ui 演示</p>

      {/* Tailwind CSS 基础测试 */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-gray-800">Tailwind CSS 基础样式测试</h3>
        <div className="test-tailwind">
          <p>🧪 测试样式类 (test-tailwind)</p>
        </div>
        <div className="p-4 bg-blue-100 border border-blue-300 rounded-lg">
          <p className="text-blue-800">✅ Tailwind CSS v4 正常工作！这个蓝色背景证明了样式已经生效。</p>
        </div>
        <div className="flex gap-2">
          <div className="w-4 h-4 bg-red-500 rounded"></div>
          <div className="w-4 h-4 bg-green-500 rounded"></div>
          <div className="w-4 h-4 bg-blue-500 rounded"></div>
          <div className="w-4 h-4 bg-yellow-500 rounded"></div>
          <span className="text-sm text-gray-600">颜色测试</span>
        </div>
      </div>
      
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">按钮组件</h3>
        <div className="flex gap-2 flex-wrap">
          <Button>默认按钮</Button>
          <Button variant="secondary">次要按钮</Button>
          <Button variant="outline">轮廓按钮</Button>
          <Button variant="ghost">幽灵按钮</Button>
          <Button variant="destructive">危险按钮</Button>
          <Button variant="link">链接按钮</Button>
        </div>
        
        <div className="flex gap-2 flex-wrap">
          <Button size="sm">小按钮</Button>
          <Button size="default">默认按钮</Button>
          <Button size="lg">大按钮</Button>
          <Button size="icon">🚀</Button>
        </div>
      </div>

      <div className="space-y-4">
        <h3 className="text-lg font-semibold">卡片组件</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <Card>
            <CardHeader>
              <CardTitle>卡片标题</CardTitle>
              <CardDescription>这是一个卡片描述，展示了 shadcn/ui 的卡片组件。</CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-gray-600">
                这里是卡片的主要内容区域。你可以在这里放置任何内容。
              </p>
            </CardContent>
            <CardFooter>
              <Button className="w-full">操作按钮</Button>
            </CardFooter>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>另一个卡片</CardTitle>
              <CardDescription>展示多个卡片的布局效果。</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="h-2 bg-blue-600 rounded"></div>
                <div className="h-2 bg-gray-300 rounded w-3/4"></div>
                <div className="h-2 bg-green-500 rounded w-1/2"></div>
              </div>
            </CardContent>
            <CardFooter className="justify-between">
              <Button variant="outline">取消</Button>
              <Button>确认</Button>
            </CardFooter>
          </Card>
        </div>
      </div>

      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-gray-800">响应式设计测试</h3>
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
          <div className="p-4 bg-purple-100 text-purple-800 rounded text-center">
            <h4 className="font-semibold">1列</h4>
            <p className="text-sm">(默认)</p>
          </div>
          <div className="p-4 bg-pink-100 text-pink-800 rounded text-center">
            <h4 className="font-semibold">2列</h4>
            <p className="text-sm">(sm+)</p>
          </div>
          <div className="p-4 bg-indigo-100 text-indigo-800 rounded text-center">
            <h4 className="font-semibold">3列</h4>
            <p className="text-sm">(md+)</p>
          </div>
          <div className="p-4 bg-teal-100 text-teal-800 rounded text-center">
            <h4 className="font-semibold">4列</h4>
            <p className="text-sm">(lg+)</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ShadcnDemo;
