import React, { useState, useEffect } from 'react';
import './App.css';
import ChatInterface from './components/ChatInterface';
import ShadcnDemo from './components/ShadcnDemo';
import './components/ChatInterface.css';
import { Button } from './components/ui/button';

// 声明vscode API类型
declare const acquireVsCodeApi: () => {
  postMessage(message: any): void;
  getState(): any;
  setState(state: any): void;
};

interface Message {
  command: string;
  data?: any;
}

const App: React.FC = () => {
  const [message, setMessage] = useState<string>('Hello from React!');
  const [count, setCount] = useState<number>(0);
  const [vscode] = useState(() => acquireVsCodeApi());

  useEffect(() => {
    // 监听来自VS Code扩展的消息
    const handleMessage = (event: MessageEvent) => {
      const message: Message = event.data;
      switch (message.command) {
        case 'updateMessage':
          setMessage(message.data);
          break;
        case 'updateCount':
          setCount(message.data);
          break;
      }
    };

    window.addEventListener('message', handleMessage);

    // 恢复状态
    const state = vscode.getState();
    if (state) {
      setMessage(state.message || message);
      setCount(state.count || count);
    }

    return () => {
      window.removeEventListener('message', handleMessage);
    };
  }, [vscode]);

  useEffect(() => {
    // 保存状态
    vscode.setState({ message, count });
  }, [message, count, vscode]);

  const sendMessageToExtension = () => {
    vscode.postMessage({
      command: 'alert',
      data: `Current count: ${count}`
    });
  };

  const incrementCount = () => {
    setCount(prev => prev + 1);
  };

  const resetCount = () => {
    setCount(0);
  };

  return (
    <div className="app">
      <header className="app-header">
        <h1>AI Code Extension</h1>
      </header>
      <main className="app-main">
        <div className="space-y-6">
          <div className="chat-section">
            <ChatInterface vscode={vscode} />
          </div>
        </div>

          <h3 className="text-lg font-semibold">按钮组件</h3>
        <div className="flex gap-2 flex-wrap">
          <Button>默认按钮</Button>
          <Button className='' variant="secondary">次要按钮</Button>
          <Button variant="outline">轮廓按钮</Button>
          <Button variant="ghost">幽灵按钮</Button>
          <Button variant="destructive">危险按钮</Button>
          <Button variant="link">链接按钮</Button>
        </div>
        
      </main>
    </div>
  );
};

export default App;
