import React, { useState } from 'react';
import './App.css';
import ChatInterface from './components/ChatInterface';

// 声明vscode API类型
declare const acquireVsCodeApi: () => {
  postMessage(message: any): void;
  getState(): any;
  setState(state: any): void;
};



const App: React.FC = () => {
  const [vscode] = useState(() => acquireVsCodeApi());



  return (
    <div className="min-h-screen bg-background p-4">
      <div className="container mx-auto max-w-4xl">
        <div className="mb-6">
          <h1 className="text-2xl font-bold text-foreground">AI Code Extension</h1>
          <p className="text-muted-foreground mt-2">与 AI 助手对话，获取代码帮助和建议</p>
        </div>
        <div className="h-[calc(100vh-200px)]">
          <ChatInterface vscode={vscode} />
        </div>
      </div>
    </div>
  );
};

export default App;
