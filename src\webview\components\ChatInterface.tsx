import React, { useState, useRef, useEffect } from 'react';
import { But<PERSON> } from './ui/button';
import { Card, CardContent, CardHeader, CardTitle } from './ui/card';
import { Textarea } from './ui/textarea';
import { Badge } from './ui/badge';
import { Avatar, AvatarFallback } from './ui/avatar';
import { ScrollArea } from './ui/scroll-area';
import { cn } from '../lib/utils';
import MessageContent from './MessageContent';

interface Message {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: Date;
}

interface ChatInterfaceProps {
  vscode: any;
}

const ChatInterface: React.FC<ChatInterfaceProps> = ({ vscode }) => {
  const [messages, setMessages] = useState<Message[]>([]);
  const [input, setInput] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isStreaming, setIsStreaming] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const currentStreamingMessage = useRef<string>('');

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  useEffect(() => {
    // 监听来自扩展的消息
    const handleMessage = (event: MessageEvent) => {
      const message = event.data;
      
      switch (message.command) {
        case 'aiResponse':
          setIsLoading(false);
          setIsStreaming(false);
          const assistantMessage: Message = {
            id: Date.now().toString(),
            role: 'assistant',
            content: message.content,
            timestamp: new Date()
          };
          setMessages(prev => [...prev, assistantMessage]);
          break;
          
        case 'aiStreamChunk':
          setIsStreaming(true);
          currentStreamingMessage.current += message.content;
          // 更新最后一条消息或创建新的流式消息
          setMessages(prev => {
            const lastMessage = prev[prev.length - 1];
            if (lastMessage && lastMessage.role === 'assistant' && lastMessage.id === 'streaming') {
              return [
                ...prev.slice(0, -1),
                { ...lastMessage, content: currentStreamingMessage.current }
              ];
            } else {
              return [
                ...prev,
                {
                  id: 'streaming',
                  role: 'assistant',
                  content: currentStreamingMessage.current,
                  timestamp: new Date()
                }
              ];
            }
          });
          break;
          
        case 'aiStreamEnd':
          setIsLoading(false);
          setIsStreaming(false);
          // 将流式消息转换为正式消息
          setMessages(prev => {
            const lastMessage = prev[prev.length - 1];
            if (lastMessage && lastMessage.id === 'streaming') {
              return [
                ...prev.slice(0, -1),
                { ...lastMessage, id: Date.now().toString() }
              ];
            }
            return prev;
          });
          currentStreamingMessage.current = '';
          break;
          
        case 'aiError':
          setIsLoading(false);
          setIsStreaming(false);
          const errorMessage: Message = {
            id: Date.now().toString(),
            role: 'assistant',
            content: `❌ 错误: ${message.error}`,
            timestamp: new Date()
          };
          setMessages(prev => [...prev, errorMessage]);
          break;
      }
    };

    window.addEventListener('message', handleMessage);
    return () => window.removeEventListener('message', handleMessage);
  }, []);

  const sendMessage = async () => {
    if (!input.trim() || isLoading) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      role: 'user',
      content: input.trim(),
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setInput('');
    setIsLoading(true);
    currentStreamingMessage.current = '';

    // 发送消息到扩展
    vscode.postMessage({
      command: 'sendChatMessage',
      message: input.trim(),
      messages: messages.map(msg => ({
        role: msg.role,
        content: msg.content
      }))
    });
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      sendMessage();
    }
  };

  const clearChat = () => {
    setMessages([]);
    currentStreamingMessage.current = '';
  };

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      // 可以添加一个 toast 通知
    } catch (err) {
      console.error('Failed to copy text: ', err);
    }
  };

  return (
    <Card className="h-full max-h-[600px] flex flex-col">
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4">
        <CardTitle className="text-lg font-semibold flex items-center gap-2">
          🤖 AI 代码助手
        </CardTitle>
        <Button
          variant="outline"
          size="sm"
          onClick={clearChat}
          disabled={isLoading}
        >
          清空对话
        </Button>
      </CardHeader>

      <CardContent className="flex-1 flex flex-col p-0">
        <ScrollArea className="flex-1 px-6">
          <div className="space-y-4 py-4">
            {messages.map((message) => (
              <div
                key={message.id}
                className={cn(
                  "flex gap-3",
                  message.role === 'user' ? 'justify-end' : 'justify-start'
                )}
              >
                {message.role === 'assistant' && (
                  <Avatar className="h-8 w-8 mt-1">
                    <AvatarFallback className="bg-primary text-primary-foreground text-sm">
                      AI
                    </AvatarFallback>
                  </Avatar>
                )}

                <div className={cn(
                  "max-w-[80%] space-y-2 group",
                  message.role === 'user' ? 'items-end' : 'items-start'
                )}>
                  <div className="flex items-center gap-2 text-xs text-muted-foreground">
                    <span className="font-medium">
                      {message.role === 'user' ? '你' : 'AI'}
                    </span>
                    <Badge variant="secondary" className="text-xs">
                      {message.timestamp.toLocaleTimeString()}
                    </Badge>
                    {message.role === 'assistant' && (
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-6 w-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
                        onClick={() => copyToClipboard(message.content)}
                        title="复制消息"
                      >
                        📋
                      </Button>
                    )}
                  </div>

                  <div className={cn(
                    "rounded-lg px-4 py-3 text-sm relative",
                    message.role === 'user'
                      ? "bg-primary text-primary-foreground ml-auto"
                      : "bg-muted hover:bg-muted/80 transition-colors"
                  )}>
                    <MessageContent content={message.content} role={message.role} />
                  </div>
                </div>

                {message.role === 'user' && (
                  <Avatar className="h-8 w-8 mt-1">
                    <AvatarFallback className="bg-secondary text-secondary-foreground text-sm">
                      你
                    </AvatarFallback>
                  </Avatar>
                )}
              </div>
            ))}

            {isLoading && !isStreaming && (
              <div className="flex gap-3 justify-start">
                <Avatar className="h-8 w-8 mt-1">
                  <AvatarFallback className="bg-primary text-primary-foreground text-sm">
                    AI
                  </AvatarFallback>
                </Avatar>
                <div className="bg-muted rounded-lg px-4 py-3">
                  <div className="flex items-center gap-1">
                    <div className="flex space-x-1">
                      <div className="w-2 h-2 bg-muted-foreground rounded-full animate-bounce [animation-delay:-0.3s]"></div>
                      <div className="w-2 h-2 bg-muted-foreground rounded-full animate-bounce [animation-delay:-0.15s]"></div>
                      <div className="w-2 h-2 bg-muted-foreground rounded-full animate-bounce"></div>
                    </div>
                    <span className="text-sm text-muted-foreground ml-2">AI 正在思考...</span>
                  </div>
                </div>
              </div>
            )}

            <div ref={messagesEndRef} />
          </div>
        </ScrollArea>

        <div className="border-t p-4 bg-background/50">
          <div className="space-y-2">
            <div className="flex gap-2">
              <Textarea
                value={input}
                onChange={(e) => setInput(e.target.value)}
                onKeyDown={handleKeyDown}
                placeholder="输入你的问题..."
                disabled={isLoading}
                className="min-h-[60px] resize-none focus:ring-2 focus:ring-primary/20"
                rows={2}
              />
              <Button
                onClick={sendMessage}
                disabled={!input.trim() || isLoading}
                className="self-end px-6"
                size="lg"
              >
                {isLoading ? (
                  <div className="flex items-center gap-2">
                    <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin" />
                    发送中
                  </div>
                ) : (
                  '发送'
                )}
              </Button>
            </div>
            <div className="flex items-center justify-between text-xs text-muted-foreground">
              <div className="flex items-center gap-4">
                <span>💡 <kbd className="px-1.5 py-0.5 bg-muted rounded text-xs">Enter</kbd> 发送</span>
                <span><kbd className="px-1.5 py-0.5 bg-muted rounded text-xs">Shift</kbd> + <kbd className="px-1.5 py-0.5 bg-muted rounded text-xs">Enter</kbd> 换行</span>
              </div>
              <div className="text-xs">
                {input.length > 0 && (
                  <span className={cn(
                    input.length > 1000 ? 'text-destructive' : 'text-muted-foreground'
                  )}>
                    {input.length}/2000
                  </span>
                )}
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default ChatInterface;
